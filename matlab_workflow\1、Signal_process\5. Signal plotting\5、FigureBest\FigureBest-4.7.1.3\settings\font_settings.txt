 (ceshiPanel)=8
 (morePanel)=8
 (undoPanel)=8
设置 (btnSettings)=8
更多 (FB_more)=8
撤销 (Undo)=8
B站 (pushbutton54)=8
开发者 (developer)=8
插件 (managePlugins)=8
作品集 (portfolio)=8
测试 (ceshi)=8
 (groupup)=8
1 (num)=20
FigureBest @图通道 (msg)=10
 (checkaxespannel)=8
 (pannelpolish)=8
 (uipanel11)=10
 (uipanel17)=8
 (uipanel12)=10
关闭 (ceshi_close)=8
测试3 (ceshi_test3)=8
测试2 (ceshi_test2)=8
测试1 (ceshi_test1)=8
关闭 (more_close)=8
上传代码 (more_portfolio)=8
反馈建议 (more_feedback)=8
部署新版 (more_sync)=8
检查更新 (more_update)=8
使用教程 (more_tutorial)=8
关闭 (undo_close)=8
回退-上一步 (undo_undo)=8
建档-初始化 (undo_archive)=8
【撤销功能控制】 (ishistory)=8
扩展 (radiobutton21)=10
调整 (raidomodify)=10
导出 (radioexport)=10
样式 (radiopolish)=10
 (axnum)=9
关 (pushbutton105)=10
副框 (pushbutton98)=10
查轴号 (pushbutton38)=10
 (morePanelOfstyle)=8
预览 (previewOfFB)=15
... (moreButtonStyle)=10
保存 (saveStyle)=10
找 (searchTemplate)=10
FP (openFP)=10
出 (exportColorTemplates)=10
入 (importColorTemplates)=10
名 (renameTemplate)=10
下 (downColorTemplate)=10
上 (upColorTemplate)=10
删 (deleteColors)=10
- 用户模板 (text30)=10
应用 (useStyle)=15
粘贴 (applyStyle)=10
拷贝 (copyStyle)=10
Line1(四色_蓝红黑绿) (styleList)=10
已激活 (isColorActive)=10
增 (addColors)=10
 (colorbox)=8
- 颜色模板 (yanse)=10
- 内置风格 (yangshi)=10
自动美化 (meihua)=15
红黑(内置) (yanselist)=10
粗线全框 (yangshilist)=10
GIF (pushbutton137)=8
存.FIG (pushbutton107)=8
输出 (rotateOut)=8
图片名与路径 (text11)=10
格式与分辨率 (text10)=10
导出 (export)=15
 (format)=8
-1 (folder)=10
浏览 (pushbutton16)=10
 (picname)=10
600 (fenbianlvValue)=13
文件夹 (pushbutton17)=10
速览 (fastview)=10
视频 (pushbutton19)=8
 (fenbianlv)=10
重绘(Simu) (replot)=10
- 扩展功能按钮 (text32)=10
- 用户自定义函数 (text31)=10
处理图例 (modifyLegend)=10
编辑数据 (editData)=10
修改点排列 (modifyDensity)=10
... (manageUserFcns)=10
平直图元 (pushbutton182)=10
一维拟合 (pushbutton180)=10
曲面插值 (pushbutton139)=10
局部放大 (localmax)=10
fb_user_func (fb_plugin_name)=10
执行 (pushbutton110)=10
分布带图 (pushbutton82)=10
滤波 (pushbutton81)=10
 (uipanel19)=8
deleteOnePoint.m (userfuncs)=10
 (uipanel20)=8
 (uipanel18)=8
 (uipanel16)=8
 (uipanel21)=8
小 (pushbutton175)=8
点-密度映射 (pushbutton173)=8
尺寸映射-大 (pushbutton169)=8
关 (pushbutton168)=8
曲面网格 (pushbutton167)=8
色谱映射1 (colorBar)=8
透视 (pushbutton165)=10
逆 (pushbutton164)=8
Y-顺时针 (pushbutton163)=8
逆 (pushbutton160)=8
X刻度标签旋转-顺时针 (pushbutton158)=8
Z (pushbutton153)=10
Border (pushbutton152)=8
Tick (pushbutton151)=8
网格不透明度 (pushbutton150)=8
YZ (pushbutton149)=8
图例框 (pushbutton147)=8
应用轴标签 (zuobiaozhou)=10
透 (pushbutton143)=8
白 (pushbutton142)=8
标-彩 (pushbutton141)=8
Y (pushbutton138)=10
右键 (pushbutton136)=10
白底 (pushbutton134)=10
X刻度标签 (pushbutton132)=10
修正坐标值 (pushbutton130)=10
轴线宽 (pushbutton129)=8
细 (pushbutton128)=8
粗 (axesLineWidth)=8
关 (pushbutton126)=8
白底黑线 (pushbutton125)=8
黑 (pushbutton123)=8
彩底-白线 (pushbutton122)=8
列数 (pushbutton119)=8
位置 (pushbutton118)=8
关标题 (pushbutton117)=8
中英字体 (pushbutton116)=8
YZ (pushbutton94)=10
关 (pushbutton115)=8
置顶 (pushbutton114)=8
融合 (pushbutton111)=8
换颜色ALT+X (pushbutton109)=8
A4 (A4preview)=8
(a) (numbering)=8
粘贴|位 (pushbutton100)=8
粘贴|矢 (pushbutton99)=8
Y (pushbutton97)=8
X (pushbutton96)=8
XZ (pushbutton95)=10
小刻度-X (pushbutton78)=8
网格 (pushbutton77)=8
细 (pushbutton76)=8
粗 (pushbutton75)=8
线宽 (pushbutton93)=8
缩 (pushbutton89)=8
放 (pushbutton90)=8
轴范围 (pushbutton91)=8
2 (pushbutton83)=8
3D (pushbutton48)=10
XY (pushbutton47)=10
小 (pushbutton80)=8
标-大 (pushbutton79)=8
TimesNR (pushbutton62)=8
宋体 (songti)=8
TeX (pushbutton60)=8
LaTeX (pushbutton58)=8
字号- (pushbutton43)=8
字号+ (pushbutton41)=8
全局字样 (fontchange)=8
图例-开 (legendon)=8
 (labelpannel)=8
比例 (size11)=10
3:2 (size32)=10
剪裁 (fit)=10
4:3 (size43)=10
黑 (quse)=8
虚 (xuxian)=8
线-实 (shixian)=8
无 (qubiao)=8
查找 (searchStyle)=10
批量导出 (exportStyleTemplates)=10
批量导入 (importStyleTemplates)=10
重命名 (renameStyle)=10
删除 (deleteStyle)=10
上移 (upStyle)=10
下移 (downStyle)=10
 (color_11)=8
 (color_1)=8
 (color_15)=8
 (color_14)=8
 (color_13)=8
 (color_12)=8
 (color_10)=8
 (color_9)=8
 (color_8)=8
 (color_7)=8
 (color_6)=8
 (color_5)=8
 (color_4)=8
 (color_3)=8
 (color_2)=8
emf (radioemf)=10
pdf (radiopdf)=10
eps (radioeps)=10
tiff (radiotiff)=10
jpg (radiojpg)=10
png (radiopng)=10
自定义 (fitmethod)=10
确认 (pushbutton179)=9
关 (pushbutton178)=9
置信度 (text28)=8
a+b*x (expression)=10
自定义表达式 (text24)=8
0.95 (conflevel)=10
填充 (radiobutton25)=10
边线 (radiobutton24)=10
预测区间 (radiobutton23)=10
置信区间 (radiobutton22)=10
关 (pushbutton191)=9
删 (pushbutton190)=9
矩形块 (pushbutton189)=9
水平带 (pushbutton188)=9
竖直带 (pushbutton187)=9
横线 - (pushbutton186)=9
1 (hline2)=10
竖线 | (pushbutton185)=9
1 (vline2)=10
横线 - (pushbutton184)=9
1 (hline1)=10
竖线 | (pushbutton183)=9
1 (vline1)=10
不透明度 (text23)=8
0.7 (text22)=8
滑动条 (faceAlpha)=8
200 (surddensity)=10
插值密度 (text21)=8
linear (interpMethod)=10
插值方法 (text19)=8
关 (pushbutton177)=9
确认 (pushbutton176)=9
关 (pushbutton85)=9
确认 (pushbutton84)=9
减 (pushbutton88)=9
加 (pushbutton87)=9
图元名 DisplayName (text18)=8
 (DisplayName)=10
窗口大小 (text17)=8
滤波方法 (text16)=8
gaussian (smoothmethod)=10
5 (windowSize)=10
default T (TL)=9
default Z (ZL)=9
default Y (YL)=9
default X (XL)=9
