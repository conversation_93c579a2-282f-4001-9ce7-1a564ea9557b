================================
 FigureBest 说明文档 2025.04.03
================================

一、软件简介
------------
FigureBest（简称 FB）是一款基于 MATLAB 的科研绘图增强工具，旨在提供一键美化、风格迁移、曲面插值绘制、阴影填充、GIF 动画生成等多种数据可视化能力，帮助科研人员、工程师、学生更高效地完成高质量图表。

- 支持平台：Windows、macOS
- 适用版本：MATLAB R2016a 及以上
- 核心文件：fb.m、clcfb.m 及相关辅助脚本

------------------------------
二、安装与启动
------------------------------
1) 解压
   将获得的压缩包解压到一个具有"读"和"写"权限的本地文件夹（请勿放置在 MATLAB 安装目录toolbox、OneDrive 或其他限制写权限的文件夹中）。

2) 首次启动
   - 打开 MATLAB，切换工作目录到解压出的文件夹，运行 fb.m。
   - 首次运行时，fb.m 会自动将此文件夹及子文件夹添加到 MATLAB 路径，如有激活需求会提示进行激活操作。

3) 后续使用
   - 只需在 MATLAB 命令行输入 fb 即可启动 FigureBest，无需再次解压或额外操作。

4) 路径管理
   - fb.m 默认调用 addpath(genpath(...)) 并 savepath；这样可以保证重启 MATLAB 后也能继续识别此工具。
   - 如果想手动管理路径，不想自动切换路径，可在 fb.m 中注释掉相关命令，但需要自行确保当前工作目录或 MATLAB 路径包含 FB 文件夹。

------------------------------
三、激活说明
------------------------------
完成FB软件安装后，输入`>>fb`即可启动软件；
如果未检测到有效许可证，会自动弹出【激活窗口】；
也可以运行`>>FigureBestActivation`手动打开【激活窗口】。

FigureBest 采用许可证激活机制，具体操作和细节可查看【激活窗口】的帮助文档【？】按钮。简要说明如下：

1) 激活方式
   - 方式一：许可证文件激活
     如果你已有绑定设备的 .lic 许可证文件，可直接离线选择此方式完成激活。     
     ***注意：FB4.6及之前发布的版本许可证与FB4.7及之后的许可证不通用！
                    如果您首次使用FB4.7，必须要先输入【激活码】，联网激活下载许可证。
	    具体查找【激活码】的方式可以参见【激活窗口】中的帮助【？】按钮。
   - 方式二：激活码在线激活
     若还没有许可证文件，需要联网并输入激活码，系统会自动生成 .lic 绑定当前设备。

2) 注意事项
   - 确保 FB 文件夹具有写权限，否则无法生成许可证文件。
   - 激活码与许可证的获取方式可在微信公众号“图通道”后台回复“FBV4”。
   - 如需在本设备重复激活或其它设备上激活，则需要额外授权次数（与购买的套餐相关）。

------------------------------
四、常见注意事项
------------------------------
1) MATLAB 版本
   - 建议在 R2016a 及以上版本运行；最好是2019b。

2) 文件夹权限
   - 确认所在文件夹可写；不建议放在 Program Files 或网盘目录下。

3) 编码与字体
   - 若出现中文乱码，可尝试在 MATLAB [Preferences → General → Encoding] 中切换为 UTF-8 或 GBK 编码。
   - 乱码通常不影响主要功能。

4) 路径冲突
   - 如果系统中已有同名脚本或旧版本 FB，须检查冲突；必要时可先删除旧版本相关路径，运行`>>clcfb`，再启动新版本。

5) 升级/更新
   - 微信公众号“图通道”后台回复“FBV4”或利用软件的更新功能。

6) Java 堆内存与错误
   - 若启动卡顿或报 java 类错误，可在 MATLAB Home → Preferences → General 中增大 Java Heap Memory。

7) 系统重装或硬件更换
   - 如大幅变动硬件或重装系统，可能需要重新申请激活。次数取决于所购授权类型。

------------------------------
五、常见问题（FAQ）
------------------------------
Q1: 运行 fb 时提示“权限错误”？
A1: 说明当前目录无写权限。请把 FB 文件夹移动到一个读写无障碍的目录，如桌面或用户文件夹。

Q2: 激活后某些功能失效？
A2: 可能是 MATLAB 的缓存或路径冲突导致。可尝试在命令行执行:
    rehash toolboxcache
    clear all
    close all
    fb
   或者删除旧版本后重启 MATLAB 解决。

Q3: 已装旧版用户启动报错？
A3: 请输入clcfb启动而不是fb启动

------------------------------
六、许可证与免责声明
------------------------------
1) 许可证
   - .lic 文件绑定当前设备硬件，若需在多台设备使用，需要对应数量的授权次数。

2) 非商业用途
   - 如果不是特殊授权，禁止用于商业盈利用途的二次销售、服务或转卖。

3) 不可逆向
   - 禁止反编译或逆向分析核心功能脚本。

4) 免责条款
   - 本工具仅用于科研与教学示例，用户需自行确保绘图结果的正确性或合规性。
   - 因用户使用不当或不可控因素导致的数据丢失、损坏，本工具及作者不承担任何责任。

------------------------------
七、技术支持
------------------------------
- 微信公众号：“图通道”
- 邮箱：<EMAIL>
  (邮件主题：【FigureBest激活问题-[具体的激活码]】，正文中请说明问题，并附购买记录、订单号与详细的报错和运行截图，否则无法受理)

====================
感谢使用 FigureBest！
====================
