 (ceshiPanel)=MS Sans Serif;normal;normal
 (morePanel)=MS Sans Serif;normal;normal
 (undoPanel)=MS Sans Serif;normal;normal
设置 (btnSettings)=华文中宋;normal;normal
更多 (FB_more)=华文中宋;normal;normal
撤销 (Undo)=华文中宋;normal;normal
B站 (pushbutton54)=华文中宋;normal;normal
开发者 (developer)=华文中宋;normal;normal
插件 (managePlugins)=华文中宋;normal;normal
作品集 (portfolio)=华文中宋;normal;normal
测试 (ceshi)=华文中宋;normal;normal
 (groupup)=MS Sans Serif;normal;normal
1 (num)=华文中宋;normal;normal
FigureBest @图通道 (msg)=华文中宋;normal;normal
 (checkaxespannel)=MS Sans Serif;normal;normal
 (pannelpolish)=MS Sans Serif;normal;normal
 (uipanel11)=华文中宋;normal;normal
 (uipanel17)=MS Sans Serif;normal;normal
 (uipanel12)=华文中宋;normal;normal
关闭 (ceshi_close)=华文中宋;normal;normal
测试3 (ceshi_test3)=华文中宋;normal;normal
测试2 (ceshi_test2)=华文中宋;normal;normal
测试1 (ceshi_test1)=华文中宋;normal;normal
关闭 (more_close)=华文中宋;normal;normal
上传代码 (more_portfolio)=华文中宋;normal;normal
反馈建议 (more_feedback)=华文中宋;normal;normal
部署新版 (more_sync)=华文中宋;normal;normal
检查更新 (more_update)=华文中宋;normal;normal
使用教程 (more_tutorial)=华文中宋;normal;normal
关闭 (undo_close)=华文中宋;normal;normal
回退-上一步 (undo_undo)=华文中宋;normal;normal
建档-初始化 (undo_archive)=华文中宋;normal;normal
【撤销功能控制】 (ishistory)=华文中宋;normal;normal
扩展 (radiobutton21)=华文中宋;normal;normal
调整 (raidomodify)=华文中宋;normal;normal
导出 (radioexport)=华文中宋;normal;normal
样式 (radiopolish)=华文中宋;normal;normal
 (axnum)=华文中宋;normal;normal
关 (pushbutton105)=华文中宋;normal;normal
副框 (pushbutton98)=华文中宋;normal;normal
查轴号 (pushbutton38)=华文中宋;normal;normal
 (morePanelOfstyle)=MS Sans Serif;normal;normal
预览 (previewOfFB)=华文中宋;normal;normal
... (moreButtonStyle)=华文中宋;normal;normal
保存 (saveStyle)=华文中宋;normal;normal
找 (searchTemplate)=华文中宋;normal;normal
FP (openFP)=华文中宋;normal;normal
出 (exportColorTemplates)=华文中宋;normal;normal
入 (importColorTemplates)=华文中宋;normal;normal
名 (renameTemplate)=华文中宋;normal;normal
下 (downColorTemplate)=华文中宋;normal;normal
上 (upColorTemplate)=华文中宋;normal;normal
删 (deleteColors)=华文中宋;normal;normal
- 用户模板 (text30)=华文中宋;normal;normal
应用 (useStyle)=华文中宋;normal;normal
粘贴 (applyStyle)=华文中宋;normal;normal
拷贝 (copyStyle)=华文中宋;normal;normal
Line1(四色_蓝红黑绿) (styleList)=华文中宋;normal;normal
已激活 (isColorActive)=华文中宋;normal;normal
增 (addColors)=华文中宋;normal;normal
 (colorbox)=MS Sans Serif;normal;normal
- 颜色模板 (yanse)=华文中宋;normal;normal
- 内置风格 (yangshi)=华文中宋;normal;normal
自动美化 (meihua)=华文中宋;normal;normal
红黑(内置) (yanselist)=华文中宋;normal;normal
粗线全框 (yangshilist)=华文中宋;normal;normal
GIF (pushbutton137)=华文中宋;normal;normal
存.FIG (pushbutton107)=华文中宋;normal;normal
输出 (rotateOut)=华文中宋;normal;normal
图片名与路径 (text11)=华文中宋;normal;normal
格式与分辨率 (text10)=华文中宋;normal;normal
导出 (export)=华文中宋;normal;normal
 (format)=MS Sans Serif;normal;normal
-1 (folder)=华文中宋;normal;normal
浏览 (pushbutton16)=华文中宋;normal;normal
 (picname)=华文中宋;normal;normal
600 (fenbianlvValue)=华文中宋;normal;normal
文件夹 (pushbutton17)=华文中宋;normal;normal
速览 (fastview)=华文中宋;normal;normal
视频 (pushbutton19)=华文中宋;normal;normal
 (fenbianlv)=MS Sans Serif;normal;normal
重绘(Simu) (replot)=华文中宋;normal;normal
- 扩展功能按钮 (text32)=华文中宋;normal;normal
- 用户自定义函数 (text31)=华文中宋;normal;normal
处理图例 (modifyLegend)=华文中宋;normal;normal
编辑数据 (editData)=华文中宋;normal;normal
修改点排列 (modifyDensity)=华文中宋;normal;normal
... (manageUserFcns)=华文中宋;normal;normal
平直图元 (pushbutton182)=华文中宋;normal;normal
一维拟合 (pushbutton180)=华文中宋;normal;normal
曲面插值 (pushbutton139)=华文中宋;normal;normal
局部放大 (localmax)=华文中宋;normal;normal
fb_user_func (fb_plugin_name)=华文中宋;normal;normal
执行 (pushbutton110)=华文中宋;normal;normal
分布带图 (pushbutton82)=华文中宋;normal;normal
滤波 (pushbutton81)=华文中宋;normal;normal
 (uipanel19)=MS Sans Serif;normal;normal
deleteOnePoint.m (userfuncs)=华文中宋;normal;normal
 (uipanel20)=MS Sans Serif;normal;normal
 (uipanel18)=MS Sans Serif;normal;normal
 (uipanel16)=MS Sans Serif;normal;normal
 (uipanel21)=MS Sans Serif;normal;normal
小 (pushbutton175)=华文中宋;normal;normal
点-密度映射 (pushbutton173)=华文中宋;normal;normal
尺寸映射-大 (pushbutton169)=华文中宋;normal;normal
关 (pushbutton168)=华文中宋;normal;normal
曲面网格 (pushbutton167)=华文中宋;normal;normal
色谱映射1 (colorBar)=华文中宋;normal;normal
透视 (pushbutton165)=华文中宋;normal;normal
逆 (pushbutton164)=华文中宋;normal;normal
Y-顺时针 (pushbutton163)=华文中宋;normal;normal
逆 (pushbutton160)=华文中宋;normal;normal
X刻度标签旋转-顺时针 (pushbutton158)=华文中宋;normal;normal
Z (pushbutton153)=华文中宋;normal;normal
Border (pushbutton152)=华文中宋;normal;normal
Tick (pushbutton151)=华文中宋;normal;normal
网格不透明度 (pushbutton150)=华文中宋;normal;normal
YZ (pushbutton149)=华文中宋;normal;normal
图例框 (pushbutton147)=华文中宋;normal;normal
应用轴标签 (zuobiaozhou)=华文中宋;normal;normal
透 (pushbutton143)=华文中宋;normal;normal
白 (pushbutton142)=华文中宋;normal;normal
标-彩 (pushbutton141)=华文中宋;normal;normal
Y (pushbutton138)=华文中宋;normal;normal
右键 (pushbutton136)=华文中宋;normal;normal
白底 (pushbutton134)=华文中宋;normal;normal
X刻度标签 (pushbutton132)=华文中宋;normal;normal
修正坐标值 (pushbutton130)=华文中宋;normal;normal
轴线宽 (pushbutton129)=华文中宋;normal;normal
细 (pushbutton128)=华文中宋;normal;normal
粗 (axesLineWidth)=华文中宋;normal;normal
关 (pushbutton126)=华文中宋;normal;normal
白底黑线 (pushbutton125)=华文中宋;normal;normal
黑 (pushbutton123)=华文中宋;normal;normal
彩底-白线 (pushbutton122)=华文中宋;normal;normal
列数 (pushbutton119)=华文中宋;normal;normal
位置 (pushbutton118)=华文中宋;normal;normal
关标题 (pushbutton117)=华文中宋;normal;normal
中英字体 (pushbutton116)=华文中宋;normal;normal
YZ (pushbutton94)=华文中宋;normal;normal
关 (pushbutton115)=华文中宋;normal;normal
置顶 (pushbutton114)=华文中宋;normal;normal
融合 (pushbutton111)=华文中宋;normal;normal
换颜色ALT+X (pushbutton109)=华文中宋;normal;normal
A4 (A4preview)=华文中宋;normal;normal
(a) (numbering)=华文中宋;normal;normal
粘贴|位 (pushbutton100)=华文中宋;normal;normal
粘贴|矢 (pushbutton99)=华文中宋;normal;normal
Y (pushbutton97)=华文中宋;normal;normal
X (pushbutton96)=华文中宋;normal;normal
XZ (pushbutton95)=华文中宋;normal;normal
小刻度-X (pushbutton78)=华文中宋;normal;normal
网格 (pushbutton77)=华文中宋;normal;normal
细 (pushbutton76)=华文中宋;normal;normal
粗 (pushbutton75)=华文中宋;normal;normal
线宽 (pushbutton93)=华文中宋;normal;normal
缩 (pushbutton89)=华文中宋;normal;normal
放 (pushbutton90)=华文中宋;normal;normal
轴范围 (pushbutton91)=华文中宋;normal;normal
2 (pushbutton83)=华文中宋;normal;normal
3D (pushbutton48)=华文中宋;normal;normal
XY (pushbutton47)=华文中宋;normal;normal
小 (pushbutton80)=华文中宋;normal;normal
标-大 (pushbutton79)=华文中宋;normal;normal
TimesNR (pushbutton62)=华文中宋;normal;normal
宋体 (songti)=华文中宋;normal;normal
TeX (pushbutton60)=华文中宋;normal;normal
LaTeX (pushbutton58)=华文中宋;normal;normal
字号- (pushbutton43)=华文中宋;normal;normal
字号+ (pushbutton41)=华文中宋;normal;normal
全局字样 (fontchange)=华文中宋;normal;normal
图例-开 (legendon)=华文中宋;normal;normal
 (labelpannel)=MS Sans Serif;normal;normal
比例 (size11)=华文中宋;normal;normal
3:2 (size32)=华文中宋;normal;normal
剪裁 (fit)=华文中宋;normal;normal
4:3 (size43)=华文中宋;normal;normal
黑 (quse)=华文中宋;normal;normal
虚 (xuxian)=华文中宋;normal;normal
线-实 (shixian)=华文中宋;normal;normal
无 (qubiao)=华文中宋;normal;normal
查找 (searchStyle)=华文中宋;normal;normal
批量导出 (exportStyleTemplates)=华文中宋;normal;normal
批量导入 (importStyleTemplates)=华文中宋;normal;normal
重命名 (renameStyle)=华文中宋;normal;normal
删除 (deleteStyle)=华文中宋;normal;normal
上移 (upStyle)=华文中宋;normal;normal
下移 (downStyle)=华文中宋;normal;normal
 (color_11)=MS Sans Serif;normal;normal
 (color_1)=MS Sans Serif;normal;normal
 (color_15)=MS Sans Serif;normal;normal
 (color_14)=MS Sans Serif;normal;normal
 (color_13)=MS Sans Serif;normal;normal
 (color_12)=MS Sans Serif;normal;normal
 (color_10)=MS Sans Serif;normal;normal
 (color_9)=MS Sans Serif;normal;normal
 (color_8)=MS Sans Serif;normal;normal
 (color_7)=MS Sans Serif;normal;normal
 (color_6)=MS Sans Serif;normal;normal
 (color_5)=MS Sans Serif;normal;normal
 (color_4)=MS Sans Serif;normal;normal
 (color_3)=MS Sans Serif;normal;normal
 (color_2)=MS Sans Serif;normal;normal
emf (radioemf)=华文中宋;normal;normal
pdf (radiopdf)=华文中宋;normal;normal
eps (radioeps)=华文中宋;normal;normal
tiff (radiotiff)=华文中宋;normal;normal
jpg (radiojpg)=华文中宋;normal;normal
png (radiopng)=华文中宋;normal;normal
自定义 (fitmethod)=华文中宋;normal;normal
确认 (pushbutton179)=华文中宋;normal;normal
关 (pushbutton178)=华文中宋;normal;normal
置信度 (text28)=华文中宋;normal;normal
a+b*x (expression)=华文中宋;normal;normal
自定义表达式 (text24)=华文中宋;normal;normal
0.95 (conflevel)=华文中宋;normal;normal
填充 (radiobutton25)=华文中宋;normal;normal
边线 (radiobutton24)=华文中宋;normal;normal
预测区间 (radiobutton23)=华文中宋;normal;normal
置信区间 (radiobutton22)=华文中宋;normal;normal
关 (pushbutton191)=华文中宋;normal;normal
删 (pushbutton190)=华文中宋;normal;normal
矩形块 (pushbutton189)=华文中宋;normal;normal
水平带 (pushbutton188)=华文中宋;normal;normal
竖直带 (pushbutton187)=华文中宋;normal;normal
横线 - (pushbutton186)=华文中宋;normal;normal
1 (hline2)=华文中宋;normal;normal
竖线 | (pushbutton185)=华文中宋;normal;normal
1 (vline2)=华文中宋;normal;normal
横线 - (pushbutton184)=华文中宋;normal;normal
1 (hline1)=华文中宋;normal;normal
竖线 | (pushbutton183)=华文中宋;normal;normal
1 (vline1)=华文中宋;normal;normal
不透明度 (text23)=华文中宋;normal;normal
0.7 (text22)=华文中宋;normal;normal
滑动条 (faceAlpha)=华文中宋;normal;normal
200 (surddensity)=华文中宋;normal;normal
插值密度 (text21)=华文中宋;normal;normal
linear (interpMethod)=华文中宋;normal;normal
插值方法 (text19)=华文中宋;normal;normal
关 (pushbutton177)=华文中宋;normal;normal
确认 (pushbutton176)=华文中宋;normal;normal
关 (pushbutton85)=华文中宋;normal;normal
确认 (pushbutton84)=华文中宋;normal;normal
减 (pushbutton88)=华文中宋;normal;normal
加 (pushbutton87)=华文中宋;normal;normal
图元名 DisplayName (text18)=华文中宋;normal;normal
 (DisplayName)=华文中宋;normal;normal
窗口大小 (text17)=华文中宋;normal;normal
滤波方法 (text16)=华文中宋;normal;normal
gaussian (smoothmethod)=华文中宋;normal;normal
5 (windowSize)=华文中宋;normal;normal
default T (TL)=华文中宋;normal;normal
default Z (ZL)=华文中宋;normal;normal
default Y (YL)=华文中宋;normal;normal
default X (XL)=华文中宋;normal;normal
